package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/RemedyMate/remedymate-backend/domain/dto"
	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
)

type RemedyMateUsecase struct {
	triageService         interfaces.TriageService
	topicMappingService   interfaces.TopicMappingService
	guidanceComposer      interfaces.GuidanceComposerService
	contentService        interfaces.ContentService
	sessionRepository     interfaces.SessionRepository
}

// NewRemedyMateUsecase creates a new RemedyMate usecase
func NewRemedyMateUsecase(
	triageService interfaces.TriageService,
	topicMappingService interfaces.TopicMappingService,
	guidanceComposer interfaces.GuidanceComposerService,
	contentService interfaces.ContentService,
	sessionRepository interfaces.SessionRepository,
) interfaces.RemedyMateUsecase {
	return &RemedyMateUsecase{
		triageService:       triageService,
		topicMappingService: topicMappingService,
		guidanceComposer:    guidanceComposer,
		contentService:      contentService,
		sessionRepository:   sessionRepository,
	}
}

// ProcessSymptoms handles the complete symptom processing flow
func (rmu *RemedyMateUsecase) ProcessSymptoms(ctx context.Context, req dto.ChatRequest, userID string) (*dto.ChatResponse, error) {
	// Create symptom input
	input := entities.SymptomInput{
		Text:     req.Text,
		Language: req.Language,
	}

	// Step 1: Triage classification
	triageResult, err := rmu.triageService.ClassifySymptoms(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("triage failed: %w", err)
	}

	response := &dto.ChatResponse{
		Triage:    *triageResult,
		SessionID: generateSessionID(),
		IsOffline: false,
	}

	// If RED flag, stop here and return escalation response
	if triageResult.Level == entities.TriageLevelRed {
		// Save session with red flag result
		session := &entities.ChatSession{
			ID:        response.SessionID,
			UserID:    userID,
			Input:     input,
			Triage:    *triageResult,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		if rmu.sessionRepository != nil {
			_ = rmu.sessionRepository.SaveSession(session) // Don't fail on save error
		}
		
		return response, nil
	}

	// Step 2: Topic mapping
	topicKey, confidence, err := rmu.topicMappingService.MapToTopic(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("topic mapping failed: %w", err)
	}

	// Step 3: Compose guidance card
	guidanceCard, err := rmu.guidanceComposer.ComposeGuidance(ctx, topicKey, req.Language)
	if err != nil {
		return nil, fmt.Errorf("guidance composition failed: %w", err)
	}

	response.GuidanceCard = guidanceCard

	// Save complete session
	session := &entities.ChatSession{
		ID:           response.SessionID,
		UserID:       userID,
		Input:        input,
		Triage:       *triageResult,
		TopicKey:     topicKey,
		GuidanceCard: guidanceCard,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if rmu.sessionRepository != nil {
		_ = rmu.sessionRepository.SaveSession(session) // Don't fail on save error
	}

	// Log confidence for monitoring (in real implementation)
	_ = confidence

	return response, nil
}

// GetTriage performs only triage classification
func (rmu *RemedyMateUsecase) GetTriage(ctx context.Context, req dto.TriageRequest) (*dto.TriageResponse, error) {
	input := entities.SymptomInput{
		Text:     req.Text,
		Language: req.Language,
	}

	result, err := rmu.triageService.ClassifySymptoms(ctx, input)
	if err != nil {
		return nil, err
	}

	return &dto.TriageResponse{
		Level:     result.Level,
		RedFlags:  result.RedFlags,
		Message:   result.Message,
		SessionID: generateSessionID(),
	}, nil
}

// MapTopic performs only topic mapping
func (rmu *RemedyMateUsecase) MapTopic(ctx context.Context, req dto.TopicMapRequest) (*dto.TopicMapResponse, error) {
	input := entities.SymptomInput{
		Text:     req.Text,
		Language: req.Language,
	}

	topicKey, confidence, err := rmu.topicMappingService.MapToTopic(ctx, input)
	if err != nil {
		return nil, err
	}

	return &dto.TopicMapResponse{
		TopicKey:   topicKey,
		Confidence: confidence,
		SessionID:  generateSessionID(),
	}, nil
}

// ComposeGuidance performs only guidance composition
func (rmu *RemedyMateUsecase) ComposeGuidance(ctx context.Context, req dto.ComposeRequest) (*dto.ComposeResponse, error) {
	guidanceCard, err := rmu.guidanceComposer.ComposeGuidance(ctx, req.TopicKey, req.Language)
	if err != nil {
		return nil, err
	}

	return &dto.ComposeResponse{
		GuidanceCard: *guidanceCard,
		SessionID:    generateSessionID(),
	}, nil
}

// GetContent retrieves content for a specific topic
func (rmu *RemedyMateUsecase) GetContent(req dto.ContentRequest) (*dto.ContentResponse, error) {
	content, err := rmu.contentService.GetContentByTopic(req.TopicKey, req.Language)
	if err != nil {
		return nil, err
	}

	return &dto.ContentResponse{
		TopicKey:  req.TopicKey,
		Language:  req.Language,
		Content:   *content,
		IsOffline: false,
	}, nil
}

// GetOfflinePack retrieves the complete offline content pack
func (rmu *RemedyMateUsecase) GetOfflinePack() (*dto.OfflinePackResponse, error) {
	pack, err := rmu.contentService.GetOfflinePack()
	if err != nil {
		return nil, err
	}

	return &dto.OfflinePackResponse{
		Version:     pack.Version,
		Topics:      pack.Topics,
		TriageRules: pack.TriageRules,
		LastUpdated: pack.LastUpdated,
		SizeBytes:   pack.SizeBytes,
	}, nil
}

// GetTopics retrieves list of available topics
func (rmu *RemedyMateUsecase) GetTopics() (*dto.TopicListResponse, error) {
	blocks, err := rmu.contentService.GetApprovedBlocks()
	if err != nil {
		return nil, err
	}

	topics := make([]dto.TopicInfo, len(blocks))
	for i, block := range blocks {
		languages := make([]string, 0, len(block.Translations))
		for lang := range block.Translations {
			languages = append(languages, lang)
		}

		topics[i] = dto.TopicInfo{
			TopicKey:  block.TopicKey,
			Languages: languages,
		}
	}

	return &dto.TopicListResponse{
		Topics: topics,
		Total:  len(topics),
	}, nil
}

// SaveSession saves a session for later retrieval
func (rmu *RemedyMateUsecase) SaveSession(userID string, req dto.SessionSaveRequest) error {
	if rmu.sessionRepository == nil {
		return fmt.Errorf("session repository not available")
	}

	// In a real implementation, you might want to validate the session exists
	// For now, we'll assume it's valid
	return nil
}

// GetUserSessions retrieves saved sessions for a user
func (rmu *RemedyMateUsecase) GetUserSessions(userID string) (*dto.SessionListResponse, error) {
	if rmu.sessionRepository == nil {
		return &dto.SessionListResponse{
			Sessions: []dto.SessionInfo{},
			Total:    0,
		}, nil
	}

	sessions, err := rmu.sessionRepository.GetUserSessions(userID, 10)
	if err != nil {
		return nil, err
	}

	sessionInfos := make([]dto.SessionInfo, len(sessions))
	for i, session := range sessions {
		preview := session.Input.Text
		if len(preview) > 50 {
			preview = preview[:47] + "..."
		}

		sessionInfos[i] = dto.SessionInfo{
			SessionID: session.ID,
			TopicKey:  session.TopicKey,
			Language:  session.Input.Language,
			CreatedAt: session.CreatedAt.Format(time.RFC3339),
			Preview:   preview,
		}
	}

	return &dto.SessionListResponse{
		Sessions: sessionInfos,
		Total:    len(sessionInfos),
	}, nil
}

// generateSessionID generates a unique session ID
func generateSessionID() string {
	// Simple session ID generation - in production use UUID or similar
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}
