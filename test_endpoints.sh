#!/bin/bash

# Test script to demonstrate the sequential RemedyMate endpoint flow
# This shows how the Flutter app should call the endpoints in sequence

BASE_URL="http://localhost:8080/api/v1/remedymate"

echo "🏥 RemedyMate Backend API Test"
echo "================================"

# Test 1: Health Check
echo ""
echo "1️⃣ Testing Health Check..."
curl -s -X GET "$BASE_URL/health" | jq '.' || echo "Health check failed"

# Test 2: Triage - GREEN case (mild symptoms)
echo ""
echo "2️⃣ Testing Triage - GREEN case (mild symptoms)..."
curl -s -X POST "$BASE_URL/triage" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "I have a mild headache and feel a bit tired",
    "language": "en"
  }' | jq '.' || echo "Triage test failed"

# Test 3: Triage - RED case (emergency symptoms)
echo ""
echo "3️⃣ Testing Triage - RED case (emergency symptoms)..."
curl -s -X POST "$BASE_URL/triage" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "I have severe chest pain and difficulty breathing",
    "language": "en"
  }' | jq '.' || echo "Triage RED test failed"

# Test 4: Topic Mapping (only if triage is GREEN/YELLOW)
echo ""
echo "4️⃣ Testing Topic Mapping..."
curl -s -X POST "$BASE_URL/map-topic" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "I have stomach pain and nausea after eating",
    "language": "en"
  }' | jq '.' || echo "Topic mapping test failed"

# Test 5: Content Retrieval
echo ""
echo "5️⃣ Testing Content Retrieval..."
curl -s -X GET "$BASE_URL/content/indigestion?lang=en" | jq '.' || echo "Content retrieval test failed"

# Test 6: Available Topics
echo ""
echo "6️⃣ Testing Available Topics..."
curl -s -X GET "$BASE_URL/topics" | jq '.' || echo "Topics test failed"

# Test 7: Offline Pack
echo ""
echo "7️⃣ Testing Offline Pack..."
curl -s -X GET "$BASE_URL/offline-pack" | jq '.version, .topics | length, .size_bytes' || echo "Offline pack test failed"

echo ""
echo "✅ All tests completed!"
echo ""
echo "📱 Flutter App Flow:"
echo "1. Call POST /triage with user symptoms"
echo "2. If result is RED → Show emergency screen, stop"
echo "3. If result is GREEN/YELLOW → Call POST /map-topic"
echo "4. Take topic from step 3 → Call GET /content/{topic}"
echo "5. Display guidance card to user"
echo ""
echo "🔄 Offline Support:"
echo "- Call GET /offline-pack to cache content"
echo "- Store locally for offline use"
