package dto

import "github.com/RemedyMate/remedymate-backend/domain/entities"

// ContentRequest represents a request for content by topic key
type ContentRequest struct {
	TopicKey string `json:"topic_key" binding:"required"`
	Language string `json:"language" binding:"required" validate:"oneof=en am"`
}

// ContentResponse represents the response with content for a topic
type ContentResponse struct {
	TopicKey     string                        `json:"topic_key"`
	Language     string                        `json:"language"`
	Content      entities.ContentTranslation  `json:"content"`
	IsOffline    bool                          `json:"is_offline"`
}

// OfflinePackResponse represents the response for offline content pack
type OfflinePackResponse struct {
	Version     string                  `json:"version"`
	Topics      []entities.ApprovedBlock `json:"topics"`
	TriageRules []entities.RedFlagRule   `json:"triage_rules"`
	LastUpdated string                   `json:"last_updated"`
	SizeBytes   int64                    `json:"size_bytes"`
}

// TopicListResponse represents the response with available topics
type TopicListResponse struct {
	Topics []TopicInfo `json:"topics"`
	Total  int         `json:"total"`
}

// TopicInfo represents basic information about a topic
type TopicInfo struct {
	TopicKey    string   `json:"topic_key"`
	Languages   []string `json:"languages"`
	Description string   `json:"description,omitempty"`
}

// SessionSaveRequest represents a request to save a chat session
type SessionSaveRequest struct {
	SessionID string `json:"session_id" binding:"required"`
}

// SessionListResponse represents the response with saved sessions
type SessionListResponse struct {
	Sessions []SessionInfo `json:"sessions"`
	Total    int           `json:"total"`
}

// SessionInfo represents basic information about a saved session
type SessionInfo struct {
	SessionID string `json:"session_id"`
	TopicKey  string `json:"topic_key"`
	Language  string `json:"language"`
	CreatedAt string `json:"created_at"`
	Preview   string `json:"preview"` // First few words of the input
}
