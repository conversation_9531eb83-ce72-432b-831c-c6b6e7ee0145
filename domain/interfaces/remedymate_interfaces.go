package interfaces

import (
	"context"
	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/dto"
)

// TriageService defines the interface for symptom triage
type TriageService interface {
	ClassifySymptoms(ctx context.Context, input entities.SymptomInput) (*entities.TriageResult, error)
	ValidateInput(input entities.SymptomInput) error
}

// TopicMappingService defines the interface for mapping symptoms to topics
type TopicMappingService interface {
	MapToTopic(ctx context.Context, input entities.SymptomInput) (string, float64, error)
	GetAvailableTopics() []string
}

// ContentService defines the interface for content management
type ContentService interface {
	GetApprovedBlocks() ([]entities.ApprovedBlock, error)
	GetContentByTopic(topicKey, language string) (*entities.ContentTranslation, error)
	GetOfflinePack() (*entities.OfflinePack, error)
	LoadContent() error
	ReloadContent() error
}

// GuidanceComposerService defines the interface for composing guidance cards
type GuidanceComposerService interface {
	ComposeGuidance(ctx context.Context, topicKey, language string) (*entities.GuidanceCard, error)
	ComposeFromBlocks(topicKey, language string, content entities.ContentTranslation) (*entities.GuidanceCard, error)
}

// AIService defines the interface for AI/LLM integration
type AIService interface {
	Triage(ctx context.Context, text, language string) (*entities.TriageResult, error)
	MapTopic(ctx context.Context, text, language string) (string, float64, error)
	ComposeGuidance(ctx context.Context, topicKey, language string, content entities.ContentTranslation) (*entities.GuidanceCard, error)
	ValidateResponse(response interface{}) error
}

// SessionRepository defines the interface for session storage
type SessionRepository interface {
	SaveSession(session *entities.ChatSession) error
	GetSession(sessionID string) (*entities.ChatSession, error)
	GetUserSessions(userID string, limit int) ([]*entities.ChatSession, error)
	DeleteSession(sessionID string) error
}

// RemedyMateUsecase defines the main use case interface
type RemedyMateUsecase interface {
	ProcessSymptoms(ctx context.Context, req dto.ChatRequest, userID string) (*dto.ChatResponse, error)
	GetTriage(ctx context.Context, req dto.TriageRequest) (*dto.TriageResponse, error)
	MapTopic(ctx context.Context, req dto.TopicMapRequest) (*dto.TopicMapResponse, error)
	ComposeGuidance(ctx context.Context, req dto.ComposeRequest) (*dto.ComposeResponse, error)
	GetContent(req dto.ContentRequest) (*dto.ContentResponse, error)
	GetOfflinePack() (*dto.OfflinePackResponse, error)
	GetTopics() (*dto.TopicListResponse, error)
	SaveSession(userID string, req dto.SessionSaveRequest) error
	GetUserSessions(userID string) (*dto.SessionListResponse, error)
}

// SafetyValidator defines the interface for safety validation
type SafetyValidator interface {
	ValidateContent(content entities.ContentTranslation) error
	ValidateGuidanceCard(card entities.GuidanceCard) error
	CheckRedFlags(text, language string) ([]string, error)
	EnforceDisclaimer(card *entities.GuidanceCard) error
}
