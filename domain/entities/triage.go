package entities

import "time"

// TriageLevel represents the severity level of symptoms
type TriageLevel string

const (
	TriageLevelGreen  TriageLevel = "GREEN"  // Likely mild
	TriageLevelYellow TriageLevel = "YELLOW" // Monitor closely
	TriageLevelRed    TriageLevel = "RED"    // Seek urgent care now
)

// TriageResult represents the result of symptom triage
type TriageResult struct {
	Level    TriageLevel `json:"level" bson:"level"`
	RedFlags []string    `json:"red_flags" bson:"red_flags"`
	Message  string      `json:"message" bson:"message"`
}

// SymptomInput represents user input for symptoms
type SymptomInput struct {
	Text     string `json:"text" bson:"text"`
	Language string `json:"language" bson:"language"` // "en" or "am"
}

// ChatSession represents a user's symptom consultation session
type ChatSession struct {
	ID          string        `json:"id" bson:"_id,omitempty"`
	UserID      string        `json:"user_id,omitempty" bson:"user_id,omitempty"`
	Input       SymptomInput  `json:"input" bson:"input"`
	Triage      TriageResult  `json:"triage" bson:"triage"`
	TopicKey    string        `json:"topic_key" bson:"topic_key"`
	GuidanceCard *GuidanceCard `json:"guidance_card,omitempty" bson:"guidance_card,omitempty"`
	IsOffline   bool          `json:"is_offline" bson:"is_offline"`
	CreatedAt   time.Time     `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at" bson:"updated_at"`
}
