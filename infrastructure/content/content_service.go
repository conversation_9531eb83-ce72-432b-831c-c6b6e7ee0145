package content

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"time"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
)

type ContentService struct {
	approvedBlocks []entities.ApprovedBlock
	redFlagRules   []entities.RedFlagRule
	dataPath       string
}

// NewContentService creates a new content service instance
func NewContentService(dataPath string) interfaces.ContentService {
	service := &ContentService{
		dataPath: dataPath,
	}
	
	// Load content on initialization
	if err := service.LoadContent(); err != nil {
		// Log error but don't fail - we can still serve cached content
		fmt.Printf("Warning: Failed to load content: %v\n", err)
	}
	
	return service
}

// LoadContent loads approved blocks from JSON file
func (cs *ContentService) LoadContent() error {
	// Load approved blocks
	blocksPath := filepath.Join(cs.dataPath, "approved_block.json")
	blocksData, err := ioutil.ReadFile(blocksPath)
	if err != nil {
		return fmt.Errorf("failed to read approved blocks file: %w", err)
	}

	var blocks []entities.ApprovedBlock
	if err := json.Unmarshal(blocksData, &blocks); err != nil {
		return fmt.Errorf("failed to parse approved blocks JSON: %w", err)
	}

	cs.approvedBlocks = blocks

	// Load red flag rules (create default if file doesn't exist)
	cs.loadRedFlagRules()

	return nil
}

// ReloadContent reloads content from files
func (cs *ContentService) ReloadContent() error {
	return cs.LoadContent()
}

// GetApprovedBlocks returns all approved blocks
func (cs *ContentService) GetApprovedBlocks() ([]entities.ApprovedBlock, error) {
	if len(cs.approvedBlocks) == 0 {
		return nil, fmt.Errorf("no approved blocks loaded")
	}
	return cs.approvedBlocks, nil
}

// GetContentByTopic returns content for a specific topic and language
func (cs *ContentService) GetContentByTopic(topicKey, language string) (*entities.ContentTranslation, error) {
	for _, block := range cs.approvedBlocks {
		if block.TopicKey == topicKey {
			if content, exists := block.Translations[language]; exists {
				return &content, nil
			}
			return nil, fmt.Errorf("language '%s' not available for topic '%s'", language, topicKey)
		}
	}
	return nil, fmt.Errorf("topic '%s' not found", topicKey)
}

// GetOfflinePack returns the complete offline content pack
func (cs *ContentService) GetOfflinePack() (*entities.OfflinePack, error) {
	if len(cs.approvedBlocks) == 0 {
		return nil, fmt.Errorf("no content available for offline pack")
	}

	pack := &entities.OfflinePack{
		Version:     "1.0.0",
		Topics:      cs.approvedBlocks,
		TriageRules: cs.redFlagRules,
		LastUpdated: time.Now().Format(time.RFC3339),
	}

	// Calculate approximate size
	data, _ := json.Marshal(pack)
	pack.SizeBytes = int64(len(data))

	return pack, nil
}

// loadRedFlagRules loads or creates default red flag rules
func (cs *ContentService) loadRedFlagRules() {
	// Default red flag rules based on PRD requirements
	cs.redFlagRules = []entities.RedFlagRule{
		{
			Keywords:    []string{"chest pain", "pressure in chest", "heart attack", "crushing pain"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Chest pain or pressure",
		},
		{
			Keywords:    []string{"የደረት ህመም", "የልብ ህመም", "ደረት ላይ ጫና"},
			Language:    "am",
			Level:       entities.TriageLevelRed,
			Description: "Chest pain or pressure (Amharic)",
		},
		{
			Keywords:    []string{"difficulty breathing", "shortness of breath", "can't breathe", "gasping"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Breathing difficulties",
		},
		{
			Keywords:    []string{"መተንፈስ ችግር", "ትንፋሽ ማጣት", "መተንፈስ አለመቻል"},
			Language:    "am",
			Level:       entities.TriageLevelRed,
			Description: "Breathing difficulties (Amharic)",
		},
		{
			Keywords:    []string{"severe headache", "worst headache", "thunderclap headache", "sudden severe headache"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Severe sudden headache",
		},
		{
			Keywords:    []string{"ከባድ ራስ ምታት", "ድንገተኛ ራስ ምታት", "በጣም የከፋ ራስ ምታት"},
			Language:    "am",
			Level:       entities.TriageLevelRed,
			Description: "Severe sudden headache (Amharic)",
		},
		{
			Keywords:    []string{"suicidal", "want to die", "kill myself", "end my life"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Suicidal thoughts",
		},
		{
			Keywords:    []string{"infant", "baby", "newborn", "3 months", "under 1 year"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Infant symptoms",
		},
		{
			Keywords:    []string{"ህፃን", "ጨቅላ ህፃን", "አዲስ የተወለደ", "ከ3 ወር በታች"},
			Language:    "am",
			Level:       entities.TriageLevelRed,
			Description: "Infant symptoms (Amharic)",
		},
	}
}

// GetRedFlagRules returns the red flag rules for triage
func (cs *ContentService) GetRedFlagRules() []entities.RedFlagRule {
	return cs.redFlagRules
}

// GetAvailableTopics returns a list of available topic keys
func (cs *ContentService) GetAvailableTopics() []string {
	topics := make([]string, len(cs.approvedBlocks))
	for i, block := range cs.approvedBlocks {
		topics[i] = block.TopicKey
	}
	return topics
}
