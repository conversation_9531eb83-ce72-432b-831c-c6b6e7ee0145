package topic

import (
	"context"
	"strings"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
	"github.com/RemedyMate/remedymate-backend/infrastructure/content"
)

type TopicMappingService struct {
	contentService interfaces.ContentService
}

// NewTopicMappingService creates a new topic mapping service
func NewTopicMappingService(contentService interfaces.ContentService) interfaces.TopicMappingService {
	return &TopicMappingService{
		contentService: contentService,
	}
}

// MapToTopic maps symptom text to the most appropriate topic
func (tms *TopicMappingService) MapToTopic(ctx context.Context, input entities.SymptomInput) (string, float64, error) {
	textLower := strings.ToLower(input.Text)

	// Define keyword mappings for each topic
	topicKeywords := tms.getTopicKeywords(input.Language)

	bestTopic := ""
	bestScore := 0.0

	// Simple keyword matching algorithm
	for topic, keywords := range topicKeywords {
		score := tms.calculateScore(textLower, keywords)
		if score > bestScore {
			bestScore = score
			bestTopic = topic
		}
	}

	// Default to indigestion if no clear match (common fallback)
	if bestTopic == "" || bestScore < 0.3 {
		bestTopic = "indigestion"
		bestScore = 0.5
	}

	return bestTopic, bestScore, nil
}

// GetAvailableTopics returns list of available topics
func (tms *TopicMappingService) GetAvailableTopics() []string {
	if cs, ok := tms.contentService.(*content.ContentService); ok {
		return cs.GetAvailableTopics()
	}

	// Fallback to default topics
	return []string{"indigestion", "headache", "sore_throat", "cough", "fever", "back_pain"}
}

// getTopicKeywords returns keyword mappings for each topic by language
func (tms *TopicMappingService) getTopicKeywords(language string) map[string][]string {
	if language == "am" {
		return map[string][]string{
			"indigestion": {
				"የሆድ ቁርጠት", "ሆድ ህመም", "የምግብ መፈጨት", "ማቅለሽለሽ",
				"ማስታወክ", "የሆድ ነፋስ", "አሲድ", "ማቃጠል",
			},
			"headache": {
				"ራስ ምታት", "ራስ ህመም", "ራስ", "ግንባር ህመም", "ማዞር",
			},
			"sore_throat": {
				"የጉሮሮ ህመም", "ጉሮሮ", "መዋጥ ችግር", "የጉሮሮ ደረቀት",
			},
			"cough": {
				"ሳል", "ሳላ", "አክታ", "የደረት ህመም", "ሳል ማለት",
			},
			"fever": {
				"ትኩሳት", "ሙቀት", "ብርድ ብርድ", "ላብ", "ትኩሳት ማለት",
			},
			"back_pain": {
				"የጀርባ ህመም", "ጀርባ", "የአከርካሪ ህመም", "ወገብ ህመም",
			},
		}
	}

	// English keywords
	return map[string][]string{
		"indigestion": {
			"stomach", "indigestion", "heartburn", "acid", "bloating",
			"nausea", "upset stomach", "gas", "burning", "reflux",
		},
		"headache": {
			"headache", "head pain", "migraine", "head", "forehead",
			"temple", "dizzy", "dizziness",
		},
		"sore_throat": {
			"sore throat", "throat", "swallowing", "throat pain",
			"scratchy throat", "dry throat",
		},
		"cough": {
			"cough", "coughing", "chest", "phlegm", "mucus",
			"dry cough", "wet cough", "hacking",
		},
		"fever": {
			"fever", "temperature", "hot", "chills", "sweating",
			"feverish", "burning up",
		},
		"back_pain": {
			"back pain", "back", "spine", "lower back", "upper back",
			"backache", "spinal",
		},
	}
}

// calculateScore calculates relevance score based on keyword matches
func (tms *TopicMappingService) calculateScore(text string, keywords []string) float64 {
	matches := 0
	totalKeywords := len(keywords)

	if totalKeywords == 0 {
		return 0.0
	}

	for _, keyword := range keywords {
		if strings.Contains(text, strings.ToLower(keyword)) {
			matches++
		}
	}

	// Base score from keyword matches
	baseScore := float64(matches) / float64(totalKeywords)

	// Boost score if multiple keywords match
	if matches > 1 {
		baseScore += 0.2
	}

	// Cap at 1.0
	if baseScore > 1.0 {
		baseScore = 1.0
	}

	return baseScore
}
