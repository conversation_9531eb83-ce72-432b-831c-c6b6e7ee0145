package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

// OpenAIClient implements LLMClient using OpenAI API
type OpenAIClient struct {
	config     LLMConfig
	httpClient *http.Client
}

// NewOpenAIClient creates a new OpenAI client
func NewOpenAIClient(config LLMConfig) LLMClient {
	return &OpenAIClient{
		config: config,
		httpClient: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second,
		},
	}
}

// OpenAI API request/response structures
type OpenAIRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	MaxTokens   int       `json:"max_tokens"`
	Temperature float32   `json:"temperature"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIResponse struct {
	Choices []Choice `json:"choices"`
	Error   *APIError `json:"error,omitempty"`
}

type Choice struct {
	Message Message `json:"message"`
}

type APIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// ClassifyTriage calls OpenAI API for triage classification
func (o *OpenAIClient) ClassifyTriage(ctx context.Context, prompt string) (string, error) {
	systemPrompt := `You are a medical triage classifier. Analyze user input and determine if it describes a medical emergency.
Your ONLY output must be a single JSON object with this exact structure: 
{"level": "RED" | "YELLOW" | "GREEN", "flags": ["flag1", "flag2"]}

CRITICAL RED FLAGS (output RED): chest pain, difficulty breathing, severe bleeding, suicidal thoughts, sudden numbness, severe head injury, stroke symptoms, heart attack symptoms, severe allergic reactions, unconsciousness.

YELLOW FLAGS (output YELLOW): high fever (>38.5°C), persistent vomiting, significant pain (7+/10), dizziness with other symptoms, confusion, blood in stool/urine, severe dehydration, persistent symptoms >3 days.

GREEN: mild symptoms like common cold, minor headache, mild indigestion, minor cuts, mild fatigue.

Be conservative - when in doubt, escalate to YELLOW or RED.`

	return o.callOpenAI(ctx, systemPrompt, prompt)
}

// MapTopic calls OpenAI API for topic mapping
func (o *OpenAIClient) MapTopic(ctx context.Context, prompt string) (string, error) {
	systemPrompt := `You are a medical symptom classifier. Map user symptoms to the most appropriate topic.
Your ONLY output must be a single JSON object with this exact structure:
{"topic": "topic_key", "confidence": 0.0-1.0}

Available topics:
- indigestion: stomach pain, heartburn, nausea, bloating, acid reflux
- headache: head pain, migraine, tension headache, cluster headache
- sore_throat: throat pain, difficulty swallowing, scratchy throat
- cough: dry cough, wet cough, chest congestion, phlegm
- fever: elevated temperature, chills, sweating, feverish feeling
- back_pain: lower back pain, upper back pain, spine pain, muscle strain

Choose the topic that best matches the primary symptom. Confidence should reflect how certain you are (0.0-1.0).`

	return o.callOpenAI(ctx, systemPrompt, prompt)
}

// ComposeGuidance calls OpenAI API for guidance composition
func (o *OpenAIClient) ComposeGuidance(ctx context.Context, prompt string) (string, error) {
	systemPrompt := `You are a medical guidance composer. You MUST only use the approved content blocks provided in the prompt.
Your ONLY output must be a single JSON object with this exact structure:
{"status": "composed", "message": "Guidance composed successfully"}

CRITICAL RULES:
1. NEVER add medical advice not in the approved blocks
2. NEVER suggest dosages or specific medications
3. NEVER diagnose conditions
4. ALWAYS include the disclaimer exactly as provided
5. Only rearrange and present the approved content clearly`

	return o.callOpenAI(ctx, systemPrompt, prompt)
}

// ExtractKeywords calls OpenAI API for keyword extraction
func (o *OpenAIClient) ExtractKeywords(ctx context.Context, text string) ([]string, error) {
	systemPrompt := `Extract the main medical keywords from the user's text.
Your ONLY output must be a JSON array of strings: ["keyword1", "keyword2", "keyword3"]
Focus on symptoms, body parts, and medical terms. Maximum 5 keywords.`

	response, err := o.callOpenAI(ctx, systemPrompt, text)
	if err != nil {
		return nil, err
	}

	var keywords []string
	if err := json.Unmarshal([]byte(response), &keywords); err != nil {
		return nil, fmt.Errorf("failed to parse keywords response: %w", err)
	}

	return keywords, nil
}

// callOpenAI makes the actual API call to OpenAI
func (o *OpenAIClient) callOpenAI(ctx context.Context, systemPrompt, userPrompt string) (string, error) {
	request := OpenAIRequest{
		Model: o.config.Model,
		Messages: []Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		MaxTokens:   o.config.MaxTokens,
		Temperature: o.config.Temperature,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+o.config.APIKey)

	resp, err := o.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("API request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var response OpenAIResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if response.Error != nil {
		return "", fmt.Errorf("OpenAI API error: %s", response.Error.Message)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response choices returned")
	}

	return response.Choices[0].Message.Content, nil
}
