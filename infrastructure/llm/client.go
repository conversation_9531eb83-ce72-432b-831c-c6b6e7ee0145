package llm

import (
	"context"
)

// LLMClient defines the interface for LLM interactions
type LLMClient interface {
	ClassifyTriage(ctx context.Context, prompt string) (string, error)
	MapTopic(ctx context.Context, prompt string) (string, error)
	ComposeGuidance(ctx context.Context, prompt string) (string, error)
	ExtractKeywords(ctx context.Context, text string) ([]string, error)
}

// LLMConfig holds configuration for LLM client
type LLMConfig struct {
	APIKey      string
	Model       string
	MaxTokens   int
	Temperature float32
	Timeout     int // seconds
}

// MockLLMClient is a mock implementation for testing and development
type MockLLMClient struct {
	config LLMConfig
}

// NewMockLLMClient creates a new mock LLM client
func NewMockLLMClient(config LLMConfig) LLMClient {
	return &MockLLMClient{
		config: config,
	}
}

// ClassifyTriage provides mock triage classification
func (m *MockLLMClient) ClassifyTriage(ctx context.Context, prompt string) (string, error) {
	// Mock implementation that analyzes the prompt for red flag keywords
	// In a real implementation, this would call OpenAI API
	
	// Simple mock logic for demonstration
	if containsRedFlags(prompt) {
		return `{"level": "RED", "flags": ["emergency symptoms detected"]}`, nil
	}
	
	if containsYellowFlags(prompt) {
		return `{"level": "YELLOW", "flags": ["moderate symptoms detected"]}`, nil
	}
	
	return `{"level": "GREEN", "flags": []}`, nil
}

// MapTopic provides mock topic mapping
func (m *MockLLMClient) MapTopic(ctx context.Context, prompt string) (string, error) {
	// Mock implementation that maps symptoms to topics
	// In a real implementation, this would call OpenAI API
	
	if containsKeyword(prompt, []string{"stomach", "indigestion", "heartburn", "nausea"}) {
		return `{"topic": "indigestion", "confidence": 0.85}`, nil
	}
	
	if containsKeyword(prompt, []string{"headache", "head pain", "migraine"}) {
		return `{"topic": "headache", "confidence": 0.90}`, nil
	}
	
	if containsKeyword(prompt, []string{"throat", "sore throat", "swallowing"}) {
		return `{"topic": "sore_throat", "confidence": 0.80}`, nil
	}
	
	if containsKeyword(prompt, []string{"cough", "coughing", "chest"}) {
		return `{"topic": "cough", "confidence": 0.75}`, nil
	}
	
	if containsKeyword(prompt, []string{"fever", "temperature", "hot", "chills"}) {
		return `{"topic": "fever", "confidence": 0.85}`, nil
	}
	
	if containsKeyword(prompt, []string{"back pain", "back", "spine"}) {
		return `{"topic": "back_pain", "confidence": 0.80}`, nil
	}
	
	// Default fallback
	return `{"topic": "indigestion", "confidence": 0.50}`, nil
}

// ComposeGuidance provides mock guidance composition
func (m *MockLLMClient) ComposeGuidance(ctx context.Context, prompt string) (string, error) {
	// Mock implementation - in real implementation, this would call OpenAI API
	// For now, we'll return a simple response indicating the service is working
	return `{"status": "composed", "message": "Guidance composed from approved blocks"}`, nil
}

// ExtractKeywords provides mock keyword extraction
func (m *MockLLMClient) ExtractKeywords(ctx context.Context, text string) ([]string, error) {
	// Mock implementation - extract simple keywords
	keywords := []string{}
	
	if containsKeyword(text, []string{"pain", "hurt", "ache"}) {
		keywords = append(keywords, "pain")
	}
	
	if containsKeyword(text, []string{"fever", "hot", "temperature"}) {
		keywords = append(keywords, "fever")
	}
	
	if containsKeyword(text, []string{"nausea", "sick", "vomit"}) {
		keywords = append(keywords, "nausea")
	}
	
	return keywords, nil
}

// Helper functions for mock logic
func containsRedFlags(text string) bool {
	redFlags := []string{
		"chest pain", "heart attack", "difficulty breathing", "can't breathe",
		"suicidal", "want to die", "severe bleeding", "unconscious",
		"stroke", "paralyzed", "severe headache", "worst headache",
	}
	
	return containsKeyword(text, redFlags)
}

func containsYellowFlags(text string) bool {
	yellowFlags := []string{
		"high fever", "persistent vomiting", "severe pain", "dizziness",
		"confusion", "blood", "persistent", "worsening", "severe",
	}
	
	return containsKeyword(text, yellowFlags)
}

func containsKeyword(text string, keywords []string) bool {
	textLower := toLowerString(text)
	
	for _, keyword := range keywords {
		keywordLower := toLowerString(keyword)
		if containsSubstring(textLower, keywordLower) {
			return true
		}
	}
	
	return false
}

// Simple helper functions to avoid external dependencies
func toLowerString(s string) string {
	result := make([]byte, len(s))
	for i, b := range []byte(s) {
		if b >= 'A' && b <= 'Z' {
			result[i] = b + 32
		} else {
			result[i] = b
		}
	}
	return string(result)
}

func containsSubstring(text, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(text) < len(substr) {
		return false
	}
	
	for i := 0; i <= len(text)-len(substr); i++ {
		match := true
		for j := 0; j < len(substr); j++ {
			if text[i+j] != substr[j] {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}
	return false
}
