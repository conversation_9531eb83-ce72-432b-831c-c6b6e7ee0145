package triage

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
	"github.com/RemedyMate/remedymate-backend/infrastructure/content"
	"github.com/RemedyMate/remedymate-backend/infrastructure/llm"
)

type TriageService struct {
	contentService interfaces.ContentService
	llmClient      llm.LLMClient
}

// NewTriageService creates a new triage service instance
func NewTriageService(contentService interfaces.ContentService, llmClient llm.LLMClient) interfaces.TriageService {
	return &TriageService{
		contentService: contentService,
		llmClient:      llmClient,
	}
}

// ClassifySymptoms performs LLM-powered triage classification with keyword fallback
func (ts *TriageService) ClassifySymptoms(ctx context.Context, input entities.SymptomInput) (*entities.TriageResult, error) {
	if err := ts.ValidateInput(input); err != nil {
		return nil, err
	}

	var triageLevel entities.TriageLevel
	var detectedFlags []string
	var err error

	// 1. FIRST, TRY THE LLM FOR NUANCED UNDERSTANDING
	triageLevel, detectedFlags, err = ts.classifyWithLLM(ctx, input)
	if err != nil {
		// 2. FALLBACK: If LLM fails, use keyword matching
		log.Printf("LLM classification failed, falling back to keywords: %v", err)
		redFlagRules := ts.getRedFlagRules()
		detectedFlags = ts.checkRedFlags(input.Text, input.Language, redFlagRules)
		triageLevel = ts.determineSeverityFromFlags(detectedFlags, input.Text, input.Language)
	}

	// 3. BUILD THE RESULT
	result := &entities.TriageResult{
		Level:    triageLevel,
		RedFlags: detectedFlags,
		Message:  ts.getTriageMessage(triageLevel, input.Language),
	}

	return result, nil
}

// ValidateInput validates the symptom input
func (ts *TriageService) ValidateInput(input entities.SymptomInput) error {
	if strings.TrimSpace(input.Text) == "" {
		return fmt.Errorf("symptom text cannot be empty")
	}

	if len(input.Text) < 3 {
		return fmt.Errorf("symptom text too short (minimum 3 characters)")
	}

	if len(input.Text) > 500 {
		return fmt.Errorf("symptom text too long (maximum 500 characters)")
	}

	if input.Language != "en" && input.Language != "am" {
		return fmt.Errorf("unsupported language: %s (supported: en, am)", input.Language)
	}

	return nil
}

// classifyWithLLM performs LLM-based triage classification
func (ts *TriageService) classifyWithLLM(ctx context.Context, input entities.SymptomInput) (entities.TriageLevel, []string, error) {
	// Construct a precise prompt for the LLM
	prompt := fmt.Sprintf(`
You are a medical triage classifier. Analyze the following user input and determine if it describes a medical emergency.
Your ONLY output must be a single JSON object with this exact structure:
{"level": "RED" | "YELLOW" | "GREEN", "flags": ["flag1", "flag2"]}

CRITICAL RED FLAGS (output RED): chest pain, difficulty breathing, severe bleeding, suicidal thoughts, sudden numbness, severe head injury, stroke symptoms, heart attack symptoms, severe allergic reactions, unconsciousness, infant symptoms (<1 year), pregnancy complications.

YELLOW FLAGS (output YELLOW): high fever (>38.5°C), persistent vomiting, significant pain (7+/10), dizziness with other symptoms, confusion, blood in stool/urine, severe dehydration, persistent symptoms >3 days.

GREEN: mild symptoms like common cold, minor headache, mild indigestion, minor cuts, mild fatigue.

Be conservative - when in doubt, escalate to YELLOW or RED.

User Input (Language: %s): "%s"
	`, input.Language, input.Text)

	// Call the LLM
	response, err := ts.llmClient.ClassifyTriage(ctx, prompt)
	if err != nil {
		return entities.TriageLevelGreen, nil, fmt.Errorf("LLM API call failed: %w", err)
	}

	// Parse the LLM's JSON response
	var llmResult struct {
		Level string   `json:"level"`
		Flags []string `json:"flags"`
	}
	if err := json.Unmarshal([]byte(response), &llmResult); err != nil {
		return entities.TriageLevelGreen, nil, fmt.Errorf("failed to parse LLM response: %w", err)
	}

	// Map the string response to your TriageLevel type
	var level entities.TriageLevel
	switch llmResult.Level {
	case "RED":
		level = entities.TriageLevelRed
	case "YELLOW":
		level = entities.TriageLevelYellow
	case "GREEN":
		level = entities.TriageLevelGreen
	default:
		return entities.TriageLevelGreen, nil, fmt.Errorf("LLM returned invalid triage level: %s", llmResult.Level)
	}

	return level, llmResult.Flags, nil
}

// determineSeverityFromFlags determines severity based on keywords (fallback method)
func (ts *TriageService) determineSeverityFromFlags(flags []string, text, language string) entities.TriageLevel {
	if len(flags) > 0 {
		return entities.TriageLevelRed // In fallback, any red flag keyword means RED
	}

	// Check for yellow flags in fallback mode
	if ts.hasYellowFlags(text, language) {
		return entities.TriageLevelYellow
	}

	return entities.TriageLevelGreen
}

// getTriageMessage returns appropriate message based on triage level
func (ts *TriageService) getTriageMessage(level entities.TriageLevel, language string) string {
	switch level {
	case entities.TriageLevelRed:
		return ts.getRedFlagMessage(language)
	case entities.TriageLevelYellow:
		return ts.getYellowFlagMessage(language)
	case entities.TriageLevelGreen:
		return ts.getGreenFlagMessage(language)
	default:
		return ts.getGreenFlagMessage(language)
	}
}

// checkRedFlags checks for red flag keywords in the input text
func (ts *TriageService) checkRedFlags(text, language string, rules []entities.RedFlagRule) []string {
	var detectedFlags []string
	textLower := strings.ToLower(text)

	for _, rule := range rules {
		if rule.Language == language && rule.Level == entities.TriageLevelRed {
			for _, keyword := range rule.Keywords {
				if strings.Contains(textLower, strings.ToLower(keyword)) {
					detectedFlags = append(detectedFlags, rule.Description)
					break // Only add each rule once
				}
			}
		}
	}

	return detectedFlags
}

// hasYellowFlags checks for yellow flag indicators (moderate symptoms)
func (ts *TriageService) hasYellowFlags(text, language string) bool {
	textLower := strings.ToLower(text)

	var yellowKeywords []string
	if language == "en" {
		yellowKeywords = []string{
			"severe pain", "high fever", "persistent", "worsening",
			"blood", "vomiting", "dizziness", "confusion",
		}
	} else if language == "am" {
		yellowKeywords = []string{
			"ከባድ ህመም", "ከፍተኛ ትኩሳት", "የማያቋርጥ", "እየባሰ",
			"ደም", "ማስታወክ", "ማዞር", "ግራ መጋባት",
		}
	}

	for _, keyword := range yellowKeywords {
		if strings.Contains(textLower, strings.ToLower(keyword)) {
			return true
		}
	}

	return false
}

// getRedFlagRules gets red flag rules from content service
func (ts *TriageService) getRedFlagRules() []entities.RedFlagRule {
	// Try to get from content service using type assertion
	if cs, ok := ts.contentService.(*content.ContentService); ok {
		return cs.GetRedFlagRules()
	}

	// Fallback to basic red flag rules
	return []entities.RedFlagRule{
		{
			Keywords:    []string{"chest pain", "heart attack", "difficulty breathing"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Emergency symptoms detected",
		},
		{
			Keywords:    []string{"የደረት ህመም", "የልብ ህመም", "መተንፈስ ችግር"},
			Language:    "am",
			Level:       entities.TriageLevelRed,
			Description: "Emergency symptoms detected",
		},
	}
}

// Message helpers for different languages
func (ts *TriageService) getRedFlagMessage(language string) string {
	if language == "am" {
		return "ወዲያውኑ የህክምና እርዳታ ይፈልጉ። ወደ ቅርብ ሆስፒታል ወይም የድንገተኛ ጊዜ አገልግሎት ይሂዱ።"
	}
	return "Seek emergency care immediately. Go to the nearest hospital or emergency service."
}

func (ts *TriageService) getYellowFlagMessage(language string) string {
	if language == "am" {
		return "ምልክቶችዎን በጥንቃቄ ይከታተሉ። ካልተሻሻለ ወይም ከባሰ የህክምና ባለሙያ ያማክሩ።"
	}
	return "Monitor your symptoms closely. Consult a healthcare professional if they don't improve or worsen."
}

func (ts *TriageService) getGreenFlagMessage(language string) string {
	if language == "am" {
		return "ምልክቶችዎ ቀላል ሊሆኑ ይችላሉ። የራስ እንክብካቤ ምክሮችን ይከተሉ።"
	}
	return "Your symptoms appear to be mild. Follow self-care recommendations."
}
