package triage

import (
	"context"
	"fmt"
	"strings"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
	"github.com/RemedyMate/remedymate-backend/infrastructure/content"
)

type TriageService struct {
	contentService interfaces.ContentService
}

// NewTriageService creates a new triage service instance
func NewTriageService(contentService interfaces.ContentService) interfaces.TriageService {
	return &TriageService{
		contentService: contentService,
	}
}

// ClassifySymptoms performs triage classification using red flag detection
func (ts *TriageService) ClassifySymptoms(ctx context.Context, input entities.SymptomInput) (*entities.TriageResult, error) {
	if err := ts.ValidateInput(input); err != nil {
		return nil, err
	}

	// Get red flag rules from content service
	redFlagRules := ts.getRedFlagRules()

	// Check for red flags
	redFlags := ts.checkRedFlags(input.Text, input.Language, redFlagRules)

	var result *entities.TriageResult

	if len(redFlags) > 0 {
		// RED: Immediate care needed
		result = &entities.TriageResult{
			Level:    entities.TriageLevelRed,
			RedFlags: redFlags,
			Message:  ts.getRedFlagMessage(input.Language),
		}
	} else if ts.hasYellowFlags(input.Text, input.Language) {
		// YELLOW: Monitor closely
		result = &entities.TriageResult{
			Level:    entities.TriageLevelYellow,
			RedFlags: []string{},
			Message:  ts.getYellowFlagMessage(input.Language),
		}
	} else {
		// GREEN: Likely mild
		result = &entities.TriageResult{
			Level:    entities.TriageLevelGreen,
			RedFlags: []string{},
			Message:  ts.getGreenFlagMessage(input.Language),
		}
	}

	return result, nil
}

// ValidateInput validates the symptom input
func (ts *TriageService) ValidateInput(input entities.SymptomInput) error {
	if strings.TrimSpace(input.Text) == "" {
		return fmt.Errorf("symptom text cannot be empty")
	}

	if len(input.Text) < 3 {
		return fmt.Errorf("symptom text too short (minimum 3 characters)")
	}

	if len(input.Text) > 500 {
		return fmt.Errorf("symptom text too long (maximum 500 characters)")
	}

	if input.Language != "en" && input.Language != "am" {
		return fmt.Errorf("unsupported language: %s (supported: en, am)", input.Language)
	}

	return nil
}

// checkRedFlags checks for red flag keywords in the input text
func (ts *TriageService) checkRedFlags(text, language string, rules []entities.RedFlagRule) []string {
	var detectedFlags []string
	textLower := strings.ToLower(text)

	for _, rule := range rules {
		if rule.Language == language && rule.Level == entities.TriageLevelRed {
			for _, keyword := range rule.Keywords {
				if strings.Contains(textLower, strings.ToLower(keyword)) {
					detectedFlags = append(detectedFlags, rule.Description)
					break // Only add each rule once
				}
			}
		}
	}

	return detectedFlags
}

// hasYellowFlags checks for yellow flag indicators (moderate symptoms)
func (ts *TriageService) hasYellowFlags(text, language string) bool {
	textLower := strings.ToLower(text)

	var yellowKeywords []string
	if language == "en" {
		yellowKeywords = []string{
			"severe pain", "high fever", "persistent", "worsening",
			"blood", "vomiting", "dizziness", "confusion",
		}
	} else if language == "am" {
		yellowKeywords = []string{
			"ከባድ ህመም", "ከፍተኛ ትኩሳት", "የማያቋርጥ", "እየባሰ",
			"ደም", "ማስታወክ", "ማዞር", "ግራ መጋባት",
		}
	}

	for _, keyword := range yellowKeywords {
		if strings.Contains(textLower, strings.ToLower(keyword)) {
			return true
		}
	}

	return false
}

// getRedFlagRules gets red flag rules from content service
func (ts *TriageService) getRedFlagRules() []entities.RedFlagRule {
	// Try to get from content service using type assertion
	if cs, ok := ts.contentService.(*content.ContentService); ok {
		return cs.GetRedFlagRules()
	}

	// Fallback to basic red flag rules
	return []entities.RedFlagRule{
		{
			Keywords:    []string{"chest pain", "heart attack", "difficulty breathing"},
			Language:    "en",
			Level:       entities.TriageLevelRed,
			Description: "Emergency symptoms detected",
		},
		{
			Keywords:    []string{"የደረት ህመም", "የልብ ህመም", "መተንፈስ ችግር"},
			Language:    "am",
			Level:       entities.TriageLevelRed,
			Description: "Emergency symptoms detected",
		},
	}
}

// Message helpers for different languages
func (ts *TriageService) getRedFlagMessage(language string) string {
	if language == "am" {
		return "ወዲያውኑ የህክምና እርዳታ ይፈልጉ። ወደ ቅርብ ሆስፒታል ወይም የድንገተኛ ጊዜ አገልግሎት ይሂዱ።"
	}
	return "Seek emergency care immediately. Go to the nearest hospital or emergency service."
}

func (ts *TriageService) getYellowFlagMessage(language string) string {
	if language == "am" {
		return "ምልክቶችዎን በጥንቃቄ ይከታተሉ። ካልተሻሻለ ወይም ከባሰ የህክምና ባለሙያ ያማክሩ።"
	}
	return "Monitor your symptoms closely. Consult a healthcare professional if they don't improve or worsen."
}

func (ts *TriageService) getGreenFlagMessage(language string) string {
	if language == "am" {
		return "ምልክቶችዎ ቀላል ሊሆኑ ይችላሉ። የራስ እንክብካቤ ምክሮችን ይከተሉ።"
	}
	return "Your symptoms appear to be mild. Follow self-care recommendations."
}
