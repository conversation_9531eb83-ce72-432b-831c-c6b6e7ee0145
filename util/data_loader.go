package util

import (
	"encoding/json"
	"io/ioutil"
	"os"
	"path/filepath"
)

// ApprovedBlock represents the structure of each approved block topic
// Adjust the struct fields as needed to match your JSON structure
// Use map[string]interface{} for flexible translation fields

type ApprovedBlock struct {
	TopicKey     string                 `json:"topic_key"`
	Translations map[string]interface{} `json:"translations"`
}

type RedFlag struct {
	RedFlagKeywords map[string][]string `json:"red_flag_keywords"`
}

// LoadApprovedBlocks loads approved block data from the given JSON file
func LoadApprovedBlocks(path string) ([]ApprovedBlock, error) {
	absPath, _ := filepath.Abs(path)
	file, err := os.Open(absPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	bytes, err := ioutil.ReadAll(file)
	if err != nil {
		return nil, err
	}
	var blocks []ApprovedBlock
	err = json.Unmarshal(bytes, &blocks)
	if err != nil {
		return nil, err
	}
	return blocks, nil
}

// LoadRedFlags loads red flag data from the given JSON file
func LoadRedFlags(path string) (*RedFlag, error) {
	absPath, _ := filepath.Abs(path)
	file, err := os.Open(absPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	bytes, err := ioutil.ReadAll(file)
	if err != nil {
		return nil, err
	}
	var redFlag RedFlag
	err = json.Unmarshal(bytes, &redFlag)
	if err != nil {
		return nil, err
	}
	return &redFlag, nil
}
