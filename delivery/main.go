package main

import (
	"log"
	"os"

	"github.com/RemedyMate/remedymate-backend/config"
	"github.com/RemedyMate/remedymate-backend/delivery/controllers"
	"github.com/RemedyMate/remedymate-backend/delivery/routers"
	"github.com/RemedyMate/remedymate-backend/infrastructure/auth"
	"github.com/RemedyMate/remedymate-backend/infrastructure/content"
	"github.com/RemedyMate/remedymate-backend/infrastructure/database"
	"github.com/RemedyMate/remedymate-backend/infrastructure/guidance"
	"github.com/RemedyMate/remedymate-backend/infrastructure/llm"
	"github.com/RemedyMate/remedymate-backend/infrastructure/topic"
	"github.com/RemedyMate/remedymate-backend/infrastructure/triage"
	"github.com/RemedyMate/remedymate-backend/repository"
	"github.com/RemedyMate/remedymate-backend/usecase"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found")
	}

	// Connect to MongoDB
	database.ConnectMongo()

	// Load OAuth configuration
	oauthConfig := config.LoadOAuthConfig()
	if err := oauthConfig.ValidateConfig(); err != nil {
		log.Fatalf("OAuth configuration error: %v", err)
	}

	// Initialize services
	jwtService := auth.NewJWTService()
	oauthService := auth.NewOAuthService(oauthConfig, jwtService)
	passwordService := auth.NewPasswordService()

	// Initialize repositories
	userRepo := repository.NewUserRepository()
	oauthRepo := repository.NewOAuthRepository(database.GetCollection("users"))

	// Initialize usecases
	userUsecase := usecase.NewUserUsecase(userRepo)
	oauthUsecase := usecase.NewOAuthUsecase(oauthService, oauthRepo)
	authUsecase := usecase.NewAuthUsecase(userRepo, passwordService, jwtService)

	// Initialize RemedyMate services
	contentService := content.NewContentService("./data")

	// Initialize LLM client (using mock for development)
	llmConfig := llm.LLMConfig{
		APIKey:      os.Getenv("OPENAI_API_KEY"),
		Model:       "gpt-3.5-turbo",
		MaxTokens:   150,
		Temperature: 0.1,
		Timeout:     30,
	}

	// Use mock client for development, switch to OpenAI for production
	var llmClient llm.LLMClient
	if llmConfig.APIKey != "" {
		llmClient = llm.NewOpenAIClient(llmConfig)
		log.Printf("✅ Using OpenAI LLM client")
	} else {
		llmClient = llm.NewMockLLMClient(llmConfig)
		log.Printf("⚠️  Using Mock LLM client (set OPENAI_API_KEY for production)")
	}

	triageService := triage.NewTriageService(contentService, llmClient)
	topicMappingService := topic.NewTopicMappingService(contentService)
	guidanceComposer := guidance.NewGuidanceComposerService(contentService)

	// Initialize RemedyMate usecase (session repository is optional for now)
	remedyMateUsecase := usecase.NewRemedyMateUsecase(
		triageService,
		topicMappingService,
		guidanceComposer,
		contentService,
		nil, // Session repository - can be added later
	)

	// Initialize controllers
	oauthController := controllers.NewOAuthController(oauthUsecase)
	authController := controllers.NewAuthController(authUsecase, userUsecase) // Added userUsecase
	userController := controllers.NewUserController(userUsecase)              // Re-added for profile management
	remedyMateController := controllers.NewRemedyMateController(remedyMateUsecase)

	// Setup router
	r := routers.SetupRouter(oauthController, authController, userController, remedyMateController)

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("🚀 Server starting on port %s", port)
	log.Printf("✅ OAuth endpoints: /api/v1/auth/oauth/*")
	log.Printf("✅ Login endpoint: /api/v1/auth/login")
	log.Printf("✅ Protected endpoints: /api/v1/auth/* (with JWT)")
	log.Fatal(r.Run(":" + port))
}
