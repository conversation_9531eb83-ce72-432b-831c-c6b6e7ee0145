package controllers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/RemedyMate/remedymate-backend/domain/dto"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
	"github.com/gin-gonic/gin"
)

type RemedyMateController struct {
	remedyMateUsecase interfaces.RemedyMateUsecase
}

// NewRemedyMateController creates a new RemedyMate controller
func NewRemedyMateController(remedyMateUsecase interfaces.RemedyMateUsecase) *RemedyMateController {
	return &RemedyMateController{
		remedyMateUsecase: remedyMateUsecase,
	}
}

// ProcessSymptoms handles the main chat endpoint
// POST /api/v1/remedymate/chat
func (rmc *RemedyMateController) ProcessSymptoms(c *gin.Context) {
	var req dto.ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse{
			Error:   "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID := getUserIDFromContext(c)

	response, err := rmc.remedyMateUsecase.ProcessSymptoms(c.Request.Context(), req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Failed to process symptoms",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTriage handles triage-only requests
// POST /api/v1/remedymate/triage
func (rmc *RemedyMateController) GetTriage(c *gin.Context) {
	var req dto.TriageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse{
			Error:   "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	response, err := rmc.remedyMateUsecase.GetTriage(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Triage failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// MapTopic handles topic mapping requests
// POST /api/v1/remedymate/map-topic
func (rmc *RemedyMateController) MapTopic(c *gin.Context) {
	var req dto.TopicMapRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse{
			Error:   "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	response, err := rmc.remedyMateUsecase.MapTopic(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Topic mapping failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ComposeGuidance handles guidance composition requests
// POST /api/v1/remedymate/compose
func (rmc *RemedyMateController) ComposeGuidance(c *gin.Context) {
	var req dto.ComposeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse{
			Error:   "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	response, err := rmc.remedyMateUsecase.ComposeGuidance(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Guidance composition failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetContent handles content retrieval by topic
// GET /api/v1/remedymate/content/:topic_key
func (rmc *RemedyMateController) GetContent(c *gin.Context) {
	topicKey := c.Param("topic_key")
	language := c.DefaultQuery("lang", "en")

	req := dto.ContentRequest{
		TopicKey: topicKey,
		Language: language,
	}

	response, err := rmc.remedyMateUsecase.GetContent(req)
	if err != nil {
		c.JSON(http.StatusNotFound, dto.ErrorResponse{
			Error:   "Content not found",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetOfflinePack handles offline content pack requests
// GET /api/v1/remedymate/offline-pack
func (rmc *RemedyMateController) GetOfflinePack(c *gin.Context) {
	response, err := rmc.remedyMateUsecase.GetOfflinePack()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Failed to generate offline pack",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTopics handles topic list requests
// GET /api/v1/remedymate/topics
func (rmc *RemedyMateController) GetTopics(c *gin.Context) {
	response, err := rmc.remedyMateUsecase.GetTopics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Failed to get topics",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// SaveSession handles session save requests
// POST /api/v1/remedymate/sessions/save
func (rmc *RemedyMateController) SaveSession(c *gin.Context) {
	var req dto.SessionSaveRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse{
			Error:   "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	userID := getUserIDFromContext(c)
	err := rmc.remedyMateUsecase.SaveSession(userID, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Failed to save session",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Session saved successfully"})
}

// GetUserSessions handles user session list requests
// GET /api/v1/remedymate/sessions
func (rmc *RemedyMateController) GetUserSessions(c *gin.Context) {
	userID := getUserIDFromContext(c)

	response, err := rmc.remedyMateUsecase.GetUserSessions(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse{
			Error:   "Failed to get user sessions",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// HealthCheck provides a health check endpoint for RemedyMate services
// GET /api/v1/remedymate/health
func (rmc *RemedyMateController) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   "remedymate",
		"timestamp": strconv.FormatInt(time.Now().Unix(), 10),
	})
}

// Helper function to get user ID from context
func getUserIDFromContext(c *gin.Context) string {
	// Try to get user ID from JWT claims (set by auth middleware)
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}

	// Fallback to anonymous user for public endpoints
	return "anonymous"
}
